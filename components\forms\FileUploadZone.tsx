"use client";

import React, { useState, useRef, useCallback } from 'react';
import { FiUploadCloud, FiX, FiFile } from 'react-icons/fi';

interface FileUploadZoneProps {
  onFilesChange: (files: File[]) => void;
  uploadedFiles: File[];
}

const FileUploadZone = ({ onFilesChange, uploadedFiles }: FileUploadZoneProps) => {
  const [isDragging, setIsDragging] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setIsDragging(true);
    } else if (e.type === "dragleave") {
      setIsDragging(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      onFilesChange(Array.from(e.dataTransfer.files));
    }
  }, [onFilesChange]);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      onFilesChange(Array.from(e.target.files));
    }
  };

  const handleRemoveFile = (indexToRemove: number) => {
    const newFiles = uploadedFiles.filter((_, index) => index !== indexToRemove);
    onFilesChange(newFiles, true); // Pass a flag to indicate replacement
  };

  const triggerFileSelect = () => inputRef.current?.click();

  return (
    <div className="w-full">
      <label className="block mb-4 text-2xl font-medium text-black">المرفقات</label>
      <div 
        onDragEnter={handleDrag} 
        onDragOver={handleDrag} 
        onDragLeave={handleDrag} 
        onDrop={handleDrop}
        onClick={triggerFileSelect}
        className={`flex flex-col items-center justify-center w-full min-h-[250px] p-6 rounded-lg border-2 border-dashed transition-colors cursor-pointer ${
          isDragging ? 'border-[#AC8852] bg-amber-50' : 'border-gray-300 bg-gray-50/50 hover:bg-gray-100'
        }`}
      >
        <FiUploadCloud className="text-gray-400" size={48} />
        <p className="mt-4 text-xl font-semibold text-gray-700">اسحب وأفلت المستندات هنا، أو انقر للاختيار</p>
        <p className="mt-1 text-base text-gray-500">المخططات، التصاميم، المواصفات الفنية (PDF, DWG, JPG)</p>
        <input ref={inputRef} type="file" multiple accept=".pdf,.dwg,.jpg,.jpeg,.png" onChange={handleFileSelect} className="hidden" />
      </div>

      {uploadedFiles.length > 0 && (
        <div className="mt-6">
          <h4 className="font-semibold text-lg text-gray-800 mb-2">الملفات المرفوعة:</h4>
          <ul className="space-y-2">
            {uploadedFiles.map((file, index) => (
              <li key={index} className="flex items-center justify-between p-3 bg-gray-100 rounded-md">
                <div className="flex items-center gap-3">
                  <FiFile className="text-gray-500" />
                  <span className="text-base font-medium text-gray-700">{file.name}</span>
                  <span className="text-sm text-gray-500">({(file.size / 1024).toFixed(1)} KB)</span>
                </div>
                <button type="button" onClick={() => handleRemoveFile(index)} className="text-red-500 hover:text-red-700 p-1">
                  <FiX size={20} />
                </button>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default FileUploadZone;