import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import AccountTypeCard from '@/components/cards/AccountTypeCard';
import Footer from '@/components/layout/Footer';
import { FiLogIn, FiUserPlus } from 'react-icons/fi';

const heroFeatures = [
  { id: 1, title: "موثوق ومؤمن", icon: "/temp_image.png" },
  { id: 2, title: "+10,000 مقاول معتمد", icon: "/temp_image.png" },
  { id: 3, title: "خدمة عالمية", icon: "/temp_image.png" },
];

const accountTypes = [
  { id: 1, title: "مقاول", description: "للشركات والمؤسسات التي تدير مشاريع متعددة", icon: "/temp_image.png", href: "/signup/contractor" },
  { id: 2, title: "شركات", description: "للشركات والمؤسسات التي تدير مشاريع متعددة", icon: "/temp_image.png", href: "/signup/company" },
  { id: 3, title: "عميل", description: "للأفراد الذين يبحثون عن مقاولين لمشاريعهم الشخصية", icon: "/temp_image.png", href: "/signup/client" },
];

export default function SignupPage() {
  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-grow">
        {/* Hero Section */}
        <section 
          className="relative text-white bg-cover bg-center bg-no-repeat" 
          style={{ backgroundImage: "url('/temp_image.png')" }} // placeholder for /rectangle-44.png
        >
          <div className="absolute inset-0 bg-black/40"></div> {/* Overlay for better text readability */}
          <div className="relative container mx-auto px-4 sm:px-6 lg:px-8 pt-16 pb-32 flex flex-col items-center text-center">
            <h1 className="font-black text-5xl sm:text-6xl lg:text-7xl leading-tight">
              منصة لاد
              <span className="block font-medium text-4xl sm:text-5xl mt-4">
                المكان الذي يلتقي فيه أفضل المقاولين بالعملاء المميزين
              </span>
            </h1>

            <div className="mt-16 sm:mt-24 flex flex-col sm:flex-row items-center gap-8">
              <Link href="/login">
                <Button variant="outlineWhite">
                  <FiLogIn size={30} />
                  <span>تسجيل الدخول</span>
                </Button>
              </Link>
              <Link href="/signup/"> {/* assuming this leads to the account selection */}
                <Button variant="outlineWhite">
                  <FiUserPlus size={30} />
                  <span>انشئ حسابك الان</span>
                </Button>
              </Link>
            </div>
            
            <div className="mt-20 sm:mt-32 w-full grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-16">
              {heroFeatures.map((feature) => (
                <div key={feature.id} className="flex items-center justify-center gap-4">
                  <h3 className="font-bold text-2xl lg:text-3xl whitespace-nowrap">{feature.title}</h3>
                  <div className="relative w-12 h-12">
                    <Image src={feature.icon} alt={`${feature.title} icon`} fill className="object-cover" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Account Types Section */}
        <section className="py-24 bg-gray-50">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-16 relative">
              <div className="absolute -top-24 right-0 w-48 h-48 hidden lg:block">
                  <Image src="/temp_image.png" alt="Lad mascot" fill className="object-contain" />
              </div>
              <h2 className="text-5xl font-bold leading-tight text-[#0d123d]">انضم إلى منصة LAD</h2>
              <p className="mt-4 text-2xl font-medium text-gray-600">اختر نوع حسابك للبدء في رحلتك معنا</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {accountTypes.map((account) => (
                <AccountTypeCard
                  key={account.id}
                  iconSrc={account.icon}
                  title={account.title}
                  description={account.description}
                  href={account.href}
                />
              ))}
            </div>
            
            <div className="mt-20 text-center">
                <Link href="/signup/options">
                    <Button variant="outlineGold" size="large">
                        التسجيل الان
                    </Button>
                </Link>
            </div>
          </div>
        </section>
      </main>
      
      <Footer />
    </div>
  );
}