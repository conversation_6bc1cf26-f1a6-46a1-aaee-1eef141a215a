import React from 'react';

interface StepProps {
  number: number;
  title: string;
  isActive: boolean;
  isCompleted: boolean;
}

const Step: React.FC<StepProps> = ({ number, title, isActive, isCompleted }) => {
  const circleBaseClasses = "w-12 h-12 rounded-full flex items-center justify-center font-bold text-lg transition-all duration-300 ease-in-out";
  const titleClasses = "absolute top-full mt-2 text-center text-sm font-medium transition-all duration-300 ease-in-out";

  let circleClasses, titleVisibility;

  if (isActive) {
    circleClasses = 'bg-[#AC8852] text-white scale-110';
    titleVisibility = 'opacity-100 visible';
  } else if (isCompleted) {
    circleClasses = 'bg-[#AC8852] text-white';
    titleVisibility = 'opacity-0 invisible';
  } else {
    circleClasses = 'bg-gray-200 text-gray-500';
    titleVisibility = 'opacity-0 invisible';
  }

  return (
    <div className="relative flex flex-col items-center">
      <div className={`${circleBaseClasses} ${circleClasses}`}>
        {isCompleted && !isActive ? '✓' : number}
      </div>
      <span className={`${titleClasses} ${isActive ? titleVisibility : 'opacity-0 invisible'}`}>{title}</span>
    </div>
  );
};

interface SignupStepperProps {
  steps: { number: number; title: string }[];
  currentStep: number;
}

const SignupStepper: React.FC<SignupStepperProps> = ({ steps, currentStep }) => {
  const progressPercentage = Math.max(0, ((currentStep - 1) / (steps.length - 1)) * 100);

  return (
    <div className="w-full max-w-md px-4 sm:px-0">
      <div className="relative flex items-center justify-between">
        <div className="absolute top-1/2 left-0 w-full h-1 bg-gray-200 transform -translate-y-1/2">
          <div 
            className="absolute top-0 left-0 h-full bg-[#AC8852] transition-all duration-500 ease-in-out"
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
        {steps.map((step) => (
          <div key={step.number} className="relative z-10">
            <Step 
              number={step.number} 
              title={step.title} 
              isActive={step.number === currentStep}
              isCompleted={step.number < currentStep}
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default SignupStepper;