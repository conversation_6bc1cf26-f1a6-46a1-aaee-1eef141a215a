"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import EntityTypeCard from '@/components/signup/EntityTypeCard';
import Image from 'next/image';
import { FiArrowLeft, FiArrowRight } from 'react-icons/fi';

const entityTypes = [
  { id: "individual", title: "فرد", image: "/temp_image.png" },
  { id: "company", title: "شركة", image: "/temp_image.png" },
  { id: "government", title: "جهة <br/> حكومية", image: "/temp_image.png" },
];

export default function EntityTypeStepPage() {
  const [selectedEntity, setSelectedEntity] = useState<string | null>(null);
  const router = useRouter();

  const handleNext = () => {
    if (selectedEntity) {
      console.log("Selected entity:", selectedEntity);
      // Navigate to the next step, e.g., router.push('/signup/steps/info');
    }
  };

  const handlePrevious = () => {
    router.back(); // Go back to the previous page (e.g., /signup)
  };

  return (
    <div className="w-full max-w-3xl mx-auto flex flex-col h-full">
      <div className="flex-grow">
        <div className="relative w-28 h-28 sm:w-40 sm:h-40 ml-auto mb-8">
            <Image src="/temp_image.png" alt="Lad mascot" fill className="object-contain" />
        </div>
        
        <h1 className="text-4xl md:text-5xl font-bold text-[#0d123d] text-center leading-tight">
          مرحبا بك حيث تتحول الرؤية إلى واقع
        </h1>
        <p className="mt-6 text-xl md:text-2xl text-gray-700 text-center leading-relaxed">
          نربطك بشبكة من نخبة المقاولين والموردين والمكاتب الهندسية لتبدأ مشاريعك بثقة وجودة.
        </p>
        
        <h3 className="mt-12 text-3xl font-bold text-[#ac8852] text-center">
            ابدأ مشروعك الآن!
        </h3>

        <div className="my-10 grid grid-cols-2 md:grid-cols-3 gap-4 md:gap-6">
          {entityTypes.map((entity) => (
            <EntityTypeCard
              key={entity.id}
              title={entity.title}
              imageSrc={entity.image}
              isSelected={selectedEntity === entity.id}
              onSelect={() => setSelectedEntity(entity.id)}
            />
          ))}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="mt-auto flex flex-col sm:flex-row-reverse gap-4 pt-8">
        <Button 
            size="default" 
            variant="outlineGold" 
            onClick={handleNext} 
            disabled={!selectedEntity}
            className="w-full sm:w-auto flex-grow"
        >
          <span>التالي</span>
          <FiArrowLeft size={24} />
        </Button>
        <Button 
            size="default" 
            variant="ghost" 
            onClick={handlePrevious}
            className="w-full sm:w-auto"
        >
          <FiArrowRight size={24} />
          <span>السابق</span>
        </Button>
      </div>
    </div>
  );
}