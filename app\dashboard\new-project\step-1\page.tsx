"use client";

import { useF<PERSON>, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useRouter } from 'next/navigation';
import FormField from '@/components/forms/FormField';
import { Button } from '@/components/ui/Button';
import { FiFileText, FiMapPin, FiArrowLeft } from 'react-icons/fi';

// Schema for form validation
const projectInfoSchema = z.object({
  projectTitle: z.string().min(5, 'عنوان المشروع يجب أن لا يقل عن 5 أحرف'),
  city: z.string().min(2, 'المدينة مطلوبة'),
  district: z.string().min(2, 'الحي مطلوب'),
  detailedAddress: z.string().min(10, 'العنوان التفصيلي يجب أن لا يقل عن 10 أحرف'),
});

type ProjectInfoValues = z.infer<typeof projectInfoSchema>;

// Simple map placeholder component
const MapPlaceholder = () => (
    <div className="w-full min-h-[200px] sm:min-h-[250px] bg-gray-100 rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
        <div className="text-center text-gray-500 p-4">
            <FiMapPin size={40} className="mx-auto text-gray-400" />
            <p className="mt-2 font-medium text-sm sm:text-base">سيتم عرض الخريطة هنا لتحديد الموقع</p>
        </div>
    </div>
);

export default function NewProjectStep1Page() {
  const router = useRouter();
  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm<ProjectInfoValues>({
    resolver: zodResolver(projectInfoSchema),
  });

  const onSubmit: SubmitHandler<ProjectInfoValues> = async (data) => {
    console.log('Step 1 Data:', data);
    await new Promise(resolve => setTimeout(resolve, 1000));
    router.push('/dashboard/new-project/step-2');
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8 md:space-y-10">
      <header>
        <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 flex items-baseline gap-3">
          <span className="text-[#AC8852]">01 -</span>
          <span>البيانات العامة للمشروع</span>
        </h2>
      </header>

      <FormField
        id="projectTitle"
        label="عنوان المشروع"
        placeholder="مثال: بناء فيلا سكنية في حي الياسمين"
        isRequired
        {...register('projectTitle')}
        error={errors.projectTitle?.message}
        icon={<FiFileText />}
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
        <FormField
          id="city"
          label="المدينة"
          placeholder="الرياض"
          isRequired
          {...register('city')}
          error={errors.city?.message}
          icon={<FiMapPin />}
        />
        <FormField
          id="district"
          label="الحي"
          placeholder="الياسمين"
          isRequired
          {...register('district')}
          error={errors.district?.message}
          icon={<FiMapPin />}
        />
      </div>

      <FormField
        id="detailedAddress"
        label="عنوان تفصيلي"
        placeholder="شارع العليا العام، مبنى رقم 123، الدور الرابع"
        isRequired
        {...register('detailedAddress')}
        error={errors.detailedAddress?.message}
        icon={<FiFileText />}
      />

      <div>
        <label className="block mb-3 text-lg sm:text-xl font-medium text-gray-800">
          الموقع الجغرافي
        </label>
        <MapPlaceholder />
      </div>

      <div className="flex justify-center pt-6">
        <Button 
          type="submit" 
          variant="primary" 
          size="lg" 
          className="w-full max-w-xs"
          disabled={isSubmitting}
        >
          <span>{isSubmitting ? 'جاري الحفظ...' : 'متابعة'}</span>
          <FiArrowLeft />
        </Button>
      </div>
    </form>
  );
}