import React from 'react';

interface TextareaFieldProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label: string;
  id: string;
  error?: string;
}

const TextareaField = React.forwardRef<HTMLTextAreaElement, TextareaFieldProps>(
  ({ label, id, error, ...props }, ref) => {
    return (
      <div className="w-full">
        <label htmlFor={id} className="block mb-4 text-2xl font-medium text-black">
          {label}
        </label>
        <textarea
          id={id}
          ref={ref}
          className={`w-full min-h-[150px] bg-white rounded-lg border border-solid p-5 text-xl focus:outline-none focus:ring-2 focus:ring-[#ac8852] transition ${error ? 'border-red-500' : 'border-[#cdcdcd]'}`}
          {...props}
        />
        {error && <p className="mt-2 text-sm text-red-500">{error}</p>}
      </div>
    );
  }
);
TextareaField.displayName = 'TextareaField';

export default TextareaField;