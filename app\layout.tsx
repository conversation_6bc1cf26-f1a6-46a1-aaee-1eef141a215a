import { Tajawal } from "next/font/google";
import "./globals.css"; // تأكد من وجود ملف Tailwind CSS الأساسي هنا

// إعداد الخط مع الأوزان المستخدمة في المشروع
const tajawal = Tajawal({
  subsets: ["arabic"],
  weight: ["400", "500", "700", "800", "900"],
  variable: "--font-tajawal", // تعريف متغير CSS للخط
});

export const metadata = {
  title: "منصة لاد للمقاولات",
  description: "المكان الذي يلتقي فيه أفضل المقاولين بالعملاء المميزين",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="ar" dir="rtl">
      <body className={`${tajawal.className} bg-white text-[#0d123d]`}>
        {children}
      </body>
    </html>
  );
}