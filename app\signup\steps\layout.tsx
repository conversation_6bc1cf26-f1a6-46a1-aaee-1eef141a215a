import Image from 'next/image';
import SignupStepper from '@/components/signup/SignupStepper';

const steps = [
  { number: 1, title: "نوع الكيان" },
  { number: 2, title: "ادخل بياناتك" },
  { number: 3, title: "التأكيد" },
];

export default function SignupStepsLayout({ children }: { children: React.ReactNode }) {
  // A simple way to get the current step from the URL, you might want a more robust solution
  const currentStep = 2; // Placeholder, this should be dynamic based on the route

  return (
    <div className="flex flex-col lg:flex-row min-h-screen bg-white" dir="rtl">
      {/* Form Content */}
      <div className="w-full lg:w-3/5 xl:w-2/3 flex flex-col p-6 sm:p-12 lg:p-16 order-1 lg:order-2">
        <div className="w-full max-w-3xl mx-auto flex-grow flex flex-col justify-center">
          {children}
        </div>
      </div>

      {/* Decorative Sidebar */}
      <aside className="w-full lg:w-2/5 xl:w-1/3 order-2 lg:order-1 relative bg-[#0D123D]">
        <div className="hidden lg:block absolute inset-0">
          <Image 
            src="/temp_image.png" 
            alt="Decorative background" 
            layout="fill" 
            objectFit="cover" 
            className="opacity-20"
          />
        </div>
        <div className="relative z-10 h-full flex flex-col items-center justify-center text-center text-white p-8 sm:p-12">
          <div className="mb-12 lg:mb-24">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold leading-tight">
              بخطوات بسيطة سيتم إنشاء حسابك
            </h2>
          </div>
          <div className="w-full px-4">
            <SignupStepper steps={steps} currentStep={currentStep} />
          </div>
        </div>
      </aside>
    </div>
  );
}