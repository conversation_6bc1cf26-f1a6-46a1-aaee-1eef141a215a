import { Stat, Project, Activity, SidebarMenuItem } from '@/types';
import { 
  FiGrid, FiBriefcase, FiFileText, FiDollarSign, FiMessageSquare, FiStar, FiSettings, FiBarChart2, FiCheckCircle, FiClock, FiCoffee, FiTrendingUp, FiMapPin, FiCalendar, FiTag, FiFile, FiBell
} from 'react-icons/fi';

export const statsData: Stat[] = [
  { id: 1, title: "المشاريع النشطة", value: "12", subtitle: "+2 هذا الشهر", icon: FiBriefcase, color: "#ac8852" },
  { id: 2, title: "عروض مستلمة", value: "30", subtitle: "5 قيد المراجعة", icon: FiFileText, color: "#ac8852" },
  { id: 3, title: "العقود الموقعة", value: "5", subtitle: "3 مكتملة", icon: FiFileText, color: "#ac8852" },
  { id: 4, title: "مشاريع مكتملة", value: "20", subtitle: "3 مكتملة", icon: FiCheckCircle, color: "#ac8852" },
  { id: 5, title: "اجمالي المدفوعات", value: "547,000", subtitle: "200 ريال هذا الشهر", icon: FiDollarSign, color: "#ac8852" },
];

export const sidebarMenuItems: SidebarMenuItem[] = [
  { id: 1, title: "لوحة التحكم", icon: FiGrid, href: "/dashboard", isActive: true },
  { id: 2, title: "مشاريعي", icon: FiBriefcase, href: "/dashboard/projects", isActive: false },
  { id: 3, title: "العروض المقدمة", icon: FiFileText, href: "/dashboard/offers", isActive: false },
  { id: 4, title: "العقود النشطة", icon: FiFileText, href: "/dashboard/contracts", isActive: false },
  { id: 5, title: "الدفعات والفواتير", icon: FiDollarSign, href: "/dashboard/payments", isActive: false },
  { id: 6, title: "طلب مواد البناء", icon: FiCoffee, href: "/dashboard/materials", isActive: false },
  { id: 7, title: "الرسائل", icon: FiMessageSquare, href: "/dashboard/messages", isActive: false },
  { id: 8, title: "التقييمات", icon: FiStar, href: "/dashboard/reviews", isActive: false },
  { id: 9, title: "الاعدادات", icon: FiSettings, href: "/dashboard/settings", isActive: false },
];

export const projectsData: Project[] = [
  { id: 1, title: "مجمع سكني", description: "لدي مجمع سكني يحتوي على15 شقة سكنية احتاج مقاول محترف لمعرفة تفاصيل وكل ما يلزم", location: "حي النرجس -شارع الهاشمية", status: "قيد التنفيذ", price: "850,000 ريال", startDate: "15/5/2025", progress: 35 },
  { id: 2, title: "فيلا خاصة", description: "بناء فيلا خاصة بمساحة 600 متر مربع مع تشطيبات فاخرة وحديقة خارجية.", location: "حي الياسمين", status: "بانتظار العروض", price: "1,200,000 ريال", startDate: "01/8/2025", offers: "10 عروض مستلمة" },
  { id: 3, title: "تجديد مكتب تجاري", description: "إعادة تصميم وتجديد مساحة مكتبية بمساحة 300 متر مربع.", location: "طريق الملك فهد", status: "بانتظار العروض", price: "350,000 ريال", startDate: "20/7/2025", offers: "5 عروض مستلمة" },
];

export const activitiesData: Activity[] = [
  { id: 1, title: "تم قبول عرض مشروع مجمع سكني", time: "منذ ساعتين", icon: FiCheckCircle },
  { id: 2, title: "رسالة جديدة من المقاول 'شركة البناء الحديث'", time: "منذ 16 ساعة", icon: FiMessageSquare },
  { id: 3, title: "يرجى مراجعة مواصفات مواد البناء المطلوبة", time: "منذ يومين", icon: FiFile },
];