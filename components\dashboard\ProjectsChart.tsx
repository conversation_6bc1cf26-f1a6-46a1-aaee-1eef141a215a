import Image from 'next/image';

const chartLegendData = [
  { id: 1, label: "قيد التنفيذ", color: "#8979ff" },
  { id: 2, label: "في الانتظار", color: "#ff928a" },
  { id: 3, label: "مكتملة", color: "#3bc3de" },
  { id: 4, label: "متوقفة", color: "#ffae4c" },
];

const ProjectsChart = () => {
  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm h-full flex flex-col">
      <h3 className="text-xl font-bold text-gray-800 mb-4">تقدم المشاريع</h3>
      
      {/* 
        ملاحظة للمطور: 
        لإنشاء مخطط تفاعلي، قم بتثبيت مكتبة مثل 'recharts' واستخدم مكون PieChart.
        مثال: npm install recharts
        هذا مجرد تمثيل مرئي للتصميم.
      */}
      <div className="flex-grow flex items-center justify-center my-4">
        <Image 
          src="/temp_image.png" // استخدم صورة المخطط الدائري المؤقتة هنا
          width={200} 
          height={200} 
          alt="Projects Chart Placeholder" 
          className="opacity-50"
        />
      </div>

      <ul className="grid grid-cols-2 gap-x-4 gap-y-2 mt-auto">
        {chartLegendData.map((item) => (
          <li key={item.id} className="flex items-center gap-2">
            <span
              className="w-3 h-3 rounded-full"
              style={{ backgroundColor: item.color }}
            />
            <span className="font-medium text-gray-700 text-sm">{item.label}</span>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default ProjectsChart;