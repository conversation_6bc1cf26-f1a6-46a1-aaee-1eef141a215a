import { FiCheck } from 'react-icons/fi';

interface Step {
  number: number;
  title: string;
}

interface NewProjectStepperProps {
  steps: Step[];
  currentStep: number;
}

const NewProjectStepper = ({ steps, currentStep }: NewProjectStepperProps) => {
  const progressWidth = ((currentStep - 1) / (steps.length - 1)) * 100;

  return (
    <div className="w-full max-w-4xl mx-auto px-4 sm:px-6">
      <div className="relative flex items-start justify-between">
        {/* Connector Line (RTL) */}
        <div className="absolute top-6 right-0 w-full h-1 bg-gray-200 -translate-y-1/2" />
        <div 
          className="absolute top-6 right-0 h-1 bg-[#AC8852] -translate-y-1/2 transition-all duration-500"
          style={{ width: `${progressWidth}%` }}
        />

        {/* Steps */}
        {steps.map((step) => {
          const isCompleted = currentStep > step.number;
          const isActive = currentStep === step.number;

          return (
            <div key={step.number} className="relative flex flex-col items-center justify-start text-center gap-2 z-10 w-20">
              <div
                className={`w-12 h-12 md:w-16 md:h-16 rounded-full flex items-center justify-center border-2 transition-all duration-300 ${
                  isActive 
                    ? 'bg-[#AC8852] border-[#AC8852] text-white scale-110 shadow-lg' 
                    : isCompleted 
                    ? 'bg-green-500 border-green-500 text-white' 
                    : 'bg-white border-gray-300 text-gray-400'
                }`}
              >
                {isCompleted ? (
                  <FiCheck size={32} className="md:w-8 md:h-8" />
                ) : (
                  <span className="text-2xl md:text-3xl font-bold">{step.number}</span>
                )}
              </div>
              <span 
                className={`font-medium text-xs sm:text-sm transition-all duration-300 ${
                  isActive ? 'text-[#AC8852] font-bold' : 'text-gray-500'
                }`}
              >
                {step.title}
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default NewProjectStepper;