import Image from 'next/image';
import Link from 'next/link';
import { FaFacebookF, FaTwitter, FaInstagram } from 'react-icons/fa';

const quickAccessLinks = [
  { href: "/", label: "الرئيسية" },
  { href: "/services", label: "خدماتنا" },
  { href: "/providers", label: "مزودي الخدمات" },
  { href: "/calculator", label: "حاسبة البناء" },
  { href: "/projects", label: "المشاريع" },
  { href: "/blog", label: "المدونات" },
];

const footerLinks = [
  { href: "/privacy", label: "سياسة الخصوصية" },
  { href: "/terms", label: "الشروط والاحكام" },
  { href: "/about", label: "من نحن" },
  { href: "/contact", label: "تواصل معنا" },
  { href: "/faq", label: "الاسئلة الشائعة" },
];

const Footer = () => {
  return (
    <footer className="w-full bg-white border-t border-solid border-[#cdcdcd] shadow-[0_-4px_25px_rgba(0,0,0,0.1)] py-16 px-4 sm:px-8 lg:px-16">
      <div className="container mx-auto grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-12 text-[#000f5e]">
        
        {/* Company Info */}
        <div className="space-y-4 xl:col-span-1">
          <h3 className="text-[25px] font-bold leading-[50px]">شركة لاد للمقاولات</h3>
          <p className="text-xl font-medium leading-[40px]">
            منصة موثوقة تعمل كوسيط رقمي بين طالب الخدمة ومقدم الخدمة في قطاع المقاولات، نعمل على تسهيل عمليات الطلب والعرض.
          </p>
        </div>

        {/* Quick Access */}
        <div className="space-y-4">
          <h3 className="text-[25px] font-bold leading-[50px]">الوصول السريع</h3>
          <nav className="flex flex-col space-y-2">
            {quickAccessLinks.map((link) => (
              <Link key={link.label} href={link.href} className="text-xl font-medium leading-[50px] hover:underline">
                {link.label}
              </Link>
            ))}
          </nav>
        </div>

        {/* Footer Links */}
        <div className="space-y-4">
           {/* تم ترك هذا العمود فارغًا بشكل مقصود ليتوافق مع التصميم الذي يضع "تواصل معنا" بجانب "الوصول السريع" في بعض الشاشات. يمكن إضافة محتوى هنا إذا لزم الأمر */}
           {/* يمكن دمج الروابط هنا أو تركها كما هي حسب التصميم النهائي */}
           <h3 className="text-[25px] font-bold leading-[50px] opacity-0 md:opacity-100">روابط هامة</h3>
           <nav className="flex flex-col space-y-2">
            {footerLinks.map((link) => (
              <Link key={link.label} href={link.href} className="text-xl font-medium leading-[50px] hover:underline">
                {link.label}
              </Link>
            ))}
          </nav>
        </div>

        {/* Contact Info */}
        <div className="space-y-4 relative">
          <h3 className="text-[25px] font-bold leading-[50px]">تواصل معنا عبر</h3>
          <div className="flex items-center gap-4">
              <SocialIcon Icon={FaFacebookF} href="#"/>
              <SocialIcon Icon={FaTwitter} href="#"/>
              <SocialIcon Icon={FaInstagram} href="#"/>
          </div>
          <div className="text-xl font-medium leading-[50px]">
            <span>أو عبر البريد الإلكتروني</span>
            <a href="mailto:<EMAIL>" className="block hover:underline">
              <EMAIL>
            </a>
          </div>
          <div className="absolute top-0 -right-48 hidden xl:block h-[350px] w-[250px]">
             <Image src="/temp_image.png" alt="Lad company logo" fill className="object-contain" />
          </div>
        </div>
      </div>
    </footer>
  );
};

// مكون صغير لأيقونات التواصل الاجتماعي
const SocialIcon = ({ Icon, href } : {Icon: React.ElementType, href: string}) => (
    <Link href={href} className="w-14 h-14 bg-gray-200 rounded-md flex items-center justify-center text-gray-600 hover:bg-[#000f5e] hover:text-white transition-colors">
        <Icon size={24}/>
    </Link>
)

export default Footer;