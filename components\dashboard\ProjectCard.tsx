import { Project } from '@/types';
import { FiMapPin, FiCalendar, FiTag, FiFileText } from 'react-icons/fi';
import { cva } from 'class-variance-authority';
import Link from 'next/link';

const statusStyles = cva('px-3 py-1 text-sm font-medium rounded-full inline-block', {
  variants: {
    status: {
      'قيد التنفيذ': 'bg-blue-100 text-blue-800',
      'بانتظار العروض': 'bg-yellow-100 text-yellow-800',
    },
  },
});

const ProgressBar = ({ progress }: { progress: number }) => (
    <div className='w-full'>
        <div className="flex justify-between text-sm font-medium text-gray-600 mb-1">
            <span>نسبة الإنجاز</span>
            <span className="text-green-600">{progress}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div className="bg-green-500 h-2.5 rounded-full" style={{ width: `${progress}%` }}></div>
        </div>
    </div>
);

const ProjectCard = ({ project }: { project: Project }) => {
  return (
    <article className="bg-white rounded-lg border border-gray-200 p-6 shadow-sm hover:shadow-lg transition-shadow flex flex-col gap-4">
      <div className="flex justify-between items-start">
        <h3 className="text-xl font-bold text-gray-800">{project.title}</h3>
        <span className={statusStyles({ status: project.status })}>{project.status}</span>
      </div>
      <p className="text-gray-600 leading-relaxed text-base line-clamp-2">{project.description}</p>
      
      <div className="border-t border-gray-100 pt-4 mt-auto space-y-3 text-gray-500 text-sm">
        <div className="flex items-center gap-2"><FiMapPin /> <span>{project.location}</span></div>
        <div className="grid grid-cols-2 gap-x-4">
            <div className="flex items-center gap-2"><FiCalendar /> <span>{project.startDate}</span></div>
            <div className="flex items-center gap-2"><FiTag /> <span>{project.price}</span></div>
        </div>
        {project.progress !== undefined && <ProgressBar progress={project.progress} />}
        {project.offers && <div className="flex items-center gap-2 pt-2"><FiFileText /> <span>{project.offers}</span></div>}
      </div>

      <Link href={`/dashboard/projects/${project.id}`} className="block w-full text-center mt-2 bg-[#AC8852] text-white py-2 rounded-md font-semibold hover:bg-[#b7986d] transition-colors">
          معرفة المزيد
      </Link>
    </article>
  );
};

export default ProjectCard;