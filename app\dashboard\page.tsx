import { statsData, projectsData, activitiesData } from '@/data/dashboard-data';
import StatCard from '@/components/dashboard/StatCard';
import ProjectsFeed from '@/components/dashboard/ProjectsFeed';
import ProjectsChart from '@/components/dashboard/ProjectsChart';
import RecentActivities from '@/components/dashboard/RecentActivities';

export default function DashboardPage() {
  return (
    <div className="space-y-8">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 sm:gap-6">
        {statsData.map((stat) => (
          <StatCard key={stat.id} stat={stat} />
        ))}
      </div>

      {/* Main Content Area */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
        <div className="xl:col-span-2 space-y-8">
          <ProjectsFeed projects={projectsData} />
        </div>
        <div className="space-y-8">
          <ProjectsChart />
          <RecentActivities activities={activitiesData} />
        </div>
      </div>
    </div>
  );
}