"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/Button';
import FormField from '@/components/forms/FormField';
import PasswordInput from '@/components/forms/PasswordInput';
import { FaUser, FaEnvelope, FaLock, FaPhoneAlt, FaGoogle, FaApple } from 'react-icons/fa';
import { FiUploadCloud, FiArrowLeft, FiArrowRight } from 'react-icons/fi';

// Schema for form validation using Zod
const signupSchema = z.object({
  firstName: z.string().min(1, 'الاسم الأول مطلوب'),
  lastName: z.string().optional(),
  email: z.string().min(1, 'البريد الإلكتروني مطلوب').email('صيغة البريد الإلكتروني غير صحيحة'),
  phone: z.string().min(9, 'رقم الجوال مطلوب'),
  password: z.string().min(6, 'كلمة المرور يجب أن لا تقل عن 6 أحرف'),
  confirmPassword: z.string().min(6, 'تأكيد كلمة المرور مطلوب'),
  agreeToTerms: z.boolean().refine(val => val === true, 'يجب الموافقة على الشروط والأحكام'),
}).refine(data => data.password === data.confirmPassword, {
  message: 'كلمتا المرور غير متطابقتين',
  path: ['confirmPassword'],
});

type SignupFormValues = z.infer<typeof signupSchema>;

// Small reusable components for this page
const SocialButton = ({ provider, icon }: { provider: string; icon: React.ReactNode }) => (
  <button className="flex items-center justify-center w-full h-[80px] sm:h-[95px] gap-4 bg-[#fffafa] rounded-lg border border-solid border-[#d9d9d9] hover:bg-gray-100 transition-colors text-xl sm:text-2xl font-medium text-black">
    {icon}
    <span>متابعة عبر {provider}</span>
  </button>
);

const Divider = ({ text }: { text: string }) => (
  <div className="flex items-center w-full gap-4 my-8">
    <div className="flex-grow h-px bg-gray-300" />
    <span className="text-gray-600 font-medium text-xl">{text}</span>
    <div className="flex-grow h-px bg-gray-300" />
  </div>
);

const FileUpload = ({ onFileUpload }: { onFileUpload: (files: FileList | null) => void }) => (
  <label className="flex flex-col items-center justify-center w-full gap-2 p-6 rounded-lg border-2 border-dashed border-gray-300 cursor-pointer hover:bg-gray-50 transition-colors">
    <FiUploadCloud className="text-gray-400" size={40} />
    <span className="font-medium text-gray-600 text-xl">ارفع المستندات المطلوبة (هوية شخصية)</span>
    <span className="text-sm text-gray-500">PDF, JPG, PNG</span>
    <span className="mt-2 px-4 py-2 bg-[#f0f0f5] rounded-lg border border-solid border-[#d9d9d9] text-[#0c1338] font-medium">+ اختيار الملفات</span>
    <input type="file" multiple accept=".pdf,.jpg,.jpeg,.png" onChange={(e) => onFileUpload(e.target.files)} className="hidden" />
  </label>
);

export default function SignupIndividualPage() {
  const router = useRouter();
  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm<SignupFormValues>({
    resolver: zodResolver(signupSchema),
  });

  const onSubmit: SubmitHandler<SignupFormValues> = async (data) => {
    console.log('Form data:', data);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    // router.push('/signup/steps/confirm'); // Navigate to the next step
  };

  const handleFileUpload = (files: FileList | null) => {
    if (files) {
      console.log('Files uploaded:', files);
    }
  };
  
  return (
    <div className="w-full max-w-4xl mx-auto">
      <header className="text-center mb-12">
        <h1 className="text-4xl sm:text-5xl font-bold text-gray-800">انشاء حساب جديد</h1>
        <p className="mt-4 text-2xl sm:text-3xl font-medium text-[#0d123d]">ابدا الان -- بأقل من دقيقة!</p>
      </header>
      
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <FormField label="الاسم الاول" id="firstName" icon={<FaUser />} isRequired {...register('firstName')} error={errors.firstName?.message} />
            <FormField label="الاسم الثاني" id="lastName" icon={<FaUser />} {...register('lastName')} />
        </div>
        
        <FormField label="البريد الإلكتروني" id="email" type="email" icon={<FaEnvelope />} isRequired {...register('email')} error={errors.email?.message} />

        <div className="w-full">
            <label htmlFor="phone" className="flex items-center gap-2 mb-4 text-2xl font-medium text-black">
                <FaPhoneAlt /><span>رقم الجوال</span><span className="text-red-500">*</span>
            </label>
            <div className="flex gap-2">
                <input {...register('phone')} id="phone" type="tel" placeholder='5xxxxxxxx' className={`flex-grow h-[95px] bg-white rounded-lg border border-solid px-5 text-2xl text-center focus:outline-none focus:ring-2 focus:ring-[#ac8852] transition ${errors.phone ? 'border-red-500' : 'border-[#cdcdcd]'}`} />
                <div className="flex items-center justify-center w-[172px] h-[95px] bg-white rounded-lg border border-solid border-[#cdcdcd] gap-2">
                    <div className="relative w-12 h-8">
                        <Image src="/temp_image.png" alt="علم السعودية" layout="fill" objectFit="contain" />
                    </div>
                    <span className="text-2xl font-medium">+966</span>
                </div>
            </div>
             {errors.phone && <p className="mt-2 text-sm text-red-500">{errors.phone.message}</p>}
        </div>

        <PasswordInput label="كلمة المرور" id="password" icon={<FaLock />} {...register('password')} error={errors.password?.message} />
        <PasswordInput label="تأكيد كلمة المرور" id="confirmPassword" icon={<FaLock />} {...register('confirmPassword')} error={errors.confirmPassword?.message} />
        
        <FileUpload onFileUpload={handleFileUpload} />
        
        <div className="flex items-center justify-end gap-2">
            <label htmlFor="agreeToTerms" className="text-xl text-gray-600 font-medium">
                أوافق على <Link href="/terms" className="text-[#ac8852] hover:underline">الشروط والأحكام</Link>
            </label>
            <input id="agreeToTerms" type="checkbox" {...register('agreeToTerms')} className="w-5 h-5 accent-[#ac8852]" />
        </div>
        {errors.agreeToTerms && <p className="text-sm text-red-500 text-right">{errors.agreeToTerms.message}</p>}
        
        <div className="flex flex-col sm:flex-row-reverse gap-4 pt-4">
            <Button type="submit" variant="primary" size="default" disabled={isSubmitting} className="w-full">
                {isSubmitting ? 'جاري الإنشاء...' : 'انشاء حساب'}
            </Button>
            <Button type="button" variant="ghost" size="default" onClick={() => router.back()} className="w-full">
                <FiArrowRight size={24} /> <span>السابق</span>
            </Button>
        </div>
      </form>
      
      <p className="text-center text-xl font-medium text-black mt-8">
        هل لديك حساب من قبل؟ <Link href="/login" className="font-bold text-[#ac8852] hover:underline">تسجيل الدخول</Link>
      </p>

      <Divider text="او التسجيل عبر" />
      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        <SocialButton provider="Google" icon={<FaGoogle size={28} />} />
        <SocialButton provider="Apple" icon={<FaApple size={32} />} />
      </div>
    </div>
  );
}