import { Stat } from '@/types';

const StatCard = ({ stat }: { stat: Stat }) => {
  const Icon = stat.icon; // Get the icon component from the data

  return (
    <div className="bg-white p-5 rounded-lg border border-gray-200 shadow-sm flex flex-col gap-2 transition-transform hover:-translate-y-1">
      <div className="flex justify-between items-start">
        <h3 className="font-medium text-gray-600 text-lg">{stat.title}</h3>
        <div className="bg-gray-100 p-2 rounded-lg">
           <Icon size={22} className="text-gray-500" />
        </div>
      </div>
      <p className="text-4xl font-bold text-gray-900" dir="ltr">{stat.value}</p>
      <p className="text-sm font-medium" style={{ color: stat.color }}>
        {stat.subtitle}
      </p>
    </div>
  );
};

export default StatCard;