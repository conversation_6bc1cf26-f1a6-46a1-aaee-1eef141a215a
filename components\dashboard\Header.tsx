import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ell, <PERSON>Menu, FiPlus } from 'react-icons/fi';
import Image from 'next/image';
import { Button } from '@/components/ui/Button';

interface HeaderProps {
  toggleSidebar: () => void;
}

const Header = ({ toggleSidebar }: HeaderProps) => {
  return (
    <header className="bg-white/80 backdrop-blur-sm sticky top-0 z-10 p-4 sm:p-6 border-b border-gray-200">
      <div className="flex items-center justify-between gap-4">
        {/* Left side */}
        <div className="flex items-center gap-4">
          <button 
            className="lg:hidden text-gray-600 hover:text-[#AC8852]"
            onClick={toggleSidebar}
          >
            <FiMenu size={28} />
          </button>
          <div className="hidden md:block">
            <h2 className="text-2xl sm:text-3xl font-bold text-gray-800">مرحباً بك، أحمد</h2>
            <p className="text-md sm:text-lg text-gray-500">إليك نظرة عامة على مشاريعك الحالية</p>
          </div>
        </div>

        {/* Right side */}
        <div className="flex items-center gap-2 sm:gap-4">
          <div className="relative hidden md:block">
            <FiSearch className="absolute top-1/2 right-4 -translate-y-1/2 text-gray-400" size={22} />
            <input
              type="text"
              placeholder="ابحث..."
              className="w-40 sm:w-64 h-12 bg-gray-100 rounded-full pr-10 sm:pr-12 pl-4 text-lg focus:outline-none focus:ring-2 focus:ring-[#AC8852]"
            />
          </div>
          
          <Button variant="primary" size="sm" className="hidden sm:flex items-center gap-2">
            <FiPlus size={20}/>
            <span className="hidden lg:block">مشروع جديد</span>
          </Button>

          <div className="flex items-center gap-2 sm:gap-3">
            <button className="relative text-gray-600 hover:text-[#AC8852]">
              <FiBell size={26} />
              <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full text-xs text-white">3</span>
            </button>
            <div className="w-12 h-12 sm:w-14 sm:h-14 rounded-full overflow-hidden border-2 border-[#AC8852]">
              <Image src="/temp_image.png" alt="User Avatar" width={56} height={56} />
            </div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;