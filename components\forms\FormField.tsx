import React from 'react';

interface FormFieldProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  id: string;
  icon?: React.ReactNode;
  error?: string;
  isRequired?: boolean;
}

const FormField = React.forwardRef<HTMLInputElement, FormFieldProps>(
  ({ label, id, icon, error, isRequired, ...props }, ref) => {
    return (
      <div className="w-full">
        <label htmlFor={id} className="flex items-center gap-2 mb-2 text-base sm:text-lg font-medium text-gray-800">
          {icon}
          <span>{label}</span>
          {isRequired && <span className="text-red-500">*</span>}
        </label>
        <input
          id={id}
          ref={ref}
          className={`w-full px-4 py-3 bg-white rounded-lg border transition-colors duration-200 ease-in-out text-base sm:text-lg text-right focus:outline-none focus:ring-2 focus:ring-[#ac8852] ${error ? 'border-red-500' : 'border-gray-300'}`}
          {...props}
        />
        {error && <p className="mt-1 text-xs sm:text-sm text-red-500">{error}</p>}
      </div>
    );
  }
);
FormField.displayName = 'FormField';

export default FormField;