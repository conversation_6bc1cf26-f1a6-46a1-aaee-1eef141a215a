"use client";

import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useRouter } from 'next/navigation';
import FormField from '@/components/forms/FormField';
import { Button } from '@/components/ui/Button';
import FileUploadZone from '@/components/forms/FileUploadZone';
import { FiArrowLeft, FiArrowRight } from 'react-icons/fi';

// Schema for form validation
const projectBudgetSchema = z.object({
  budget: z.coerce.number({ invalid_type_error: 'الرجاء إدخال رقم صحيح' }).positive('يجب أن تكون الميزانية أكبر من صفر'),
  duration: z.coerce.number({ invalid_type_error: 'الرجاء إدخال رقم صحيح' }).positive('يجب أن تكون المدة أكبر من صفر'),
  startDate: z.string().min(1, 'تاريخ البدء مطلوب'),
  files: z.array(z.instanceof(File)).min(1, 'يجب رفع ملف واحد على الأقل'),
});

type ProjectBudgetValues = z.infer<typeof projectBudgetSchema>;

export default function NewProjectStep3Page() {
  const router = useRouter();
  const { register, handleSubmit, formState: { errors, isSubmitting }, setValue, watch } = useForm<ProjectBudgetValues>({
    resolver: zodResolver(projectBudgetSchema),
    defaultValues: {
      files: [],
    }
  });

  const uploadedFiles = watch('files');

  const handleFilesChange = (newFiles: File[], replace = false) => {
    const currentFiles = replace ? [] : (watch('files') || []);
    const updatedFiles = [...currentFiles, ...newFiles];
    setValue('files', updatedFiles, { shouldValidate: true });
  };
  
  const onSubmit: SubmitHandler<ProjectBudgetValues> = async (data) => {
    console.log('Step 3 Data:', data);
    
    // Here you would typically upload files to a server/storage
    // and get back URLs or identifiers.
    // For now, we just simulate a delay.
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    router.push('/dashboard/new-project/step-4'); // Navigate to the final review step
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-10">
      <header>
        <h2 className="text-3xl font-bold text-gray-900 flex items-baseline gap-3">
          <span className="text-[#AC8852]">03 -</span>
          <span>الميزانية والمرفقات</span>
        </h2>
      </header>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        <FormField
          id="budget"
          label="الميزانية المتوقعة (ريال)"
          type="number"
          placeholder="500000"
          isRequired
          {...register('budget')}
          error={errors.budget?.message}
        />
        <FormField
          id="duration"
          label="مدة التنفيذ (بالأشهر)"
          type="number"
          placeholder="12"
          isRequired
          {...register('duration')}
          error={errors.duration?.message}
        />
        <FormField
          id="startDate"
          label="تاريخ البدء المطلوب"
          type="date"
          isRequired
          {...register('startDate')}
          error={errors.startDate?.message}
        />
      </div>

      <FileUploadZone onFilesChange={handleFilesChange} uploadedFiles={uploadedFiles} />
      {errors.files && <p className="text-sm text-red-500">{errors.files.message}</p>}

      <div className="flex flex-col-reverse sm:flex-row justify-center gap-4 pt-8">
        <Button 
            type="button" 
            variant="ghost" 
            size="large"
            onClick={() => router.back()}
            className="w-full max-w-sm"
        >
          <FiArrowRight size={24} /> <span>السابق</span>
        </Button>
        <Button 
          type="submit" 
          variant="primary" 
          size="large" 
          className="w-full max-w-sm !text-white"
          disabled={isSubmitting}
        >
          <span>{isSubmitting ? 'جاري الحفظ...' : 'متابعة'}</span> <FiArrowLeft size={24} />
        </Button>
      </div>
    </form>
  );
}