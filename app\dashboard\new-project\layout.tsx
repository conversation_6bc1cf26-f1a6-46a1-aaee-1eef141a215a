"use client";

import NewProjectStepper from '@/components/dashboard/new-project/NewProjectStepper';
import { usePathname } from 'next/navigation';

const steps = [
    { number: 1, title: 'البيانات العامة' },
    { number: 2, title: 'تفاصيل المشروع' },
    { number: 3, title: 'المرفقات' },
    { number: 4, title: 'المراجعة والنشر' },
];

export default function NewProjectLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  // Determine current step based on URL
  const pathSegments = pathname.split('/');
  const lastSegment = pathSegments[pathSegments.length - 1];
  let currentStep = 1;
  if (lastSegment.startsWith('step-')) {
    currentStep = parseInt(lastSegment.split('-')[1]) || 1;
  }
  
  return (
    <div className="space-y-12">
      <header className="text-center">
        <h1 className="text-4xl sm:text-5xl font-bold text-gray-800">إنشاء مشروع جديد</h1>
        <p className="mt-4 text-xl sm:text-2xl text-gray-600 max-w-3xl mx-auto">
          اتبع الخطوات التالية لإنشاء مشروعك الجديد وإرساله للمقاولين المناسبين
        </p>
      </header>
      
      <NewProjectStepper steps={steps} currentStep={currentStep} />
      
      <div className="bg-white/50 rounded-xl border border-gray-200 shadow-sm p-6 sm:p-8 md:p-12">
        {children}
      </div>
    </div>
  );
}