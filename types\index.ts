export interface Stat {
  id: number;
  title: string;
  value: string;
  subtitle: string;
  icon: React.ElementType; // Using react-icons type
  color: string;
}

export interface Project {
  id: number;
  title: string;
  description: string;
  location: string;
  status: 'قيد التنفيذ' | 'بانتظار العروض';
  price: string;
  startDate: string;
  progress?: number; // Progress as a number 0-100
  offers?: string;
}

export interface Activity {
  id: number;
  title: string;
  time: string;
  icon: React.ElementType;
}

export interface SidebarMenuItem {
  id: number;
  title: string;
  icon: React.ElementType;
  href: string;
  isActive: boolean;
}