import { Activity } from '@/types';

const ActivityItem = ({ activity }: { activity: Activity }) => {
  const Icon = activity.icon;

  return (
    <div className="flex items-start gap-4 p-3 rounded-lg hover:bg-gray-50/50 transition-colors">
      <div className="bg-gray-100 p-3 rounded-full">
        <Icon size={22} className="text-gray-600" />
      </div>
      <div className="flex-1">
        <p className="font-medium text-gray-800 text-base leading-tight">{activity.title}</p>
        <p className="text-sm text-gray-500 mt-1">{activity.time}</p>
      </div>
    </div>
  );
};

export default ActivityItem;