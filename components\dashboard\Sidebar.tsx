"use client";
import Link from 'next/link';
import Image from 'next/image';
import { usePathname } from 'next/navigation';
import { FiLogOut, FiX } from 'react-icons/fi';
import { sidebarMenuItems } from '@/data/dashboard-data';

interface SidebarProps {
  isSidebarOpen: boolean;
  toggleSidebar: () => void;
}

const Sidebar = ({ isSidebarOpen, toggleSidebar }: SidebarProps) => {
  const pathname = usePathname();

  return (
    <>
      {isSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-20 lg:hidden"
          onClick={toggleSidebar}
        ></div>
      )}

      <aside
        className={`fixed top-0 right-0 h-screen w-72 bg-[#0D123D] text-white flex flex-col justify-between z-30 transform transition-transform duration-300 ease-in-out ${
          isSidebarOpen ? 'translate-x-0' : 'translate-x-full'
        } lg:translate-x-0 lg:static lg:flex lg:h-screen`}
      >
        <div className="flex flex-col flex-1 overflow-y-auto">
          <div className="flex justify-between items-center py-8 px-6 flex-shrink-0">
            <h1 className="text-4xl font-black">منصة لاد</h1>
            <button onClick={toggleSidebar} className="lg:hidden text-white hover:text-gray-300 transition-colors">
              <FiX size={24} />
            </button>
          </div>
          <nav className="mt-10 flex flex-col items-end px-6 flex-1">
            {sidebarMenuItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.id}
                  href={item.href}
                  className={`flex items-center justify-end gap-4 w-full text-right p-4 my-1 rounded-lg transition-colors ${
                    isActive ? 'bg-[#AC8852]' : 'hover:bg-white/10'
                  }`}
                  onClick={toggleSidebar}
                >
                  <span className="text-xl font-normal">{item.title}</span>
                  <div className="flex items-center justify-center w-6 h-6">
                    <item.icon size={24} />
                  </div>
                </Link>
              );
            })}
          </nav>
        </div>
        <div className="p-6 flex-shrink-0">
          <button className="flex items-center justify-center w-full gap-4 p-4 bg-[#AC8852] rounded-lg text-xl font-medium hover:bg-[#b7986d] transition-colors">
            <span>تسجيل الخروج</span>
            <div className="flex items-center justify-center w-6 h-6">
              <FiLogOut size={24} />
            </div>
          </button>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;