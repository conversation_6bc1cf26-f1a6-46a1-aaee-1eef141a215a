"use client";

import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import RadioGroup from '@/components/forms/RadioGroup';
import CheckboxGroup from '@/components/forms/CheckboxGroup';
import TextareaField from '@/components/forms/TextareaField';
import { workTypeOptions, contractorTypeOptions } from '@/data/new-project-data';
import { FiArrowLeft, FiArrowRight } from 'react-icons/fi';

// Schema for form validation
const projectDetailsSchema = z.object({
  workType: z.string({ required_error: 'الرجاء اختيار نوع العمل' }),
  contractorTypes: z.array(z.string()).min(1, 'الرجاء اختيار تصنيف واحد على الأقل للمقاولين'),
  specialRequirements: z.string().min(20, 'المتطلبات الخاصة يجب أن لا تقل عن 20 حرفًا'),
});

type ProjectDetailsValues = z.infer<typeof projectDetailsSchema>;

export default function NewProjectStep2Page() {
  const router = useRouter();
  const { register, handleSubmit, formState: { errors, isSubmitting } } = useForm<ProjectDetailsValues>({
    resolver: zodResolver(projectDetailsSchema),
    defaultValues: {
      specialRequirements: "يفضل المقاولون الحاصلون على شهادة الجودة ISO، والذين لديهم خبرة في المشاريع التجارية لا تقل عن 5 سنوات.",
    }
  });

  const onSubmit: SubmitHandler<ProjectDetailsValues> = async (data) => {
    console.log('Step 2 Data:', data);
    await new Promise(resolve => setTimeout(resolve, 1000));
    router.push('/dashboard/new-project/step-3');
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-8 md:space-y-10">
      <header>
        <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 flex items-baseline gap-3">
          <span className="text-[#AC8852]">02 -</span>
          <span>تفاصيل المشروع والجهات المنفذة</span>
        </h2>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-start">
        <RadioGroup 
          title="نوع العمل المطلوب" 
          options={workTypeOptions} 
          register={register('workType')}
          error={errors.workType?.message}
        />
        <CheckboxGroup 
          title="تصنيف المقاولين المطلوب" 
          options={contractorTypeOptions} 
          register={register('contractorTypes')}
          error={errors.contractorTypes?.message}
        />
      </div>

      <TextareaField
        id="specialRequirements"
        label="المتطلبات الخاصة"
        {...register('specialRequirements')}
        error={errors.specialRequirements?.message}
        rows={5}
      />
      
      <div className="flex flex-col-reverse sm:flex-row justify-center gap-3 pt-6">
        <Button 
            type="button" 
            variant="outline" 
            size="lg"
            onClick={() => router.back()}
            className="w-full max-w-xs"
        >
          <FiArrowRight /> <span>السابق</span>
        </Button>
        <Button 
          type="submit" 
          variant="primary" 
          size="lg" 
          className="w-full max-w-xs"
          disabled={isSubmitting}
        >
          <span>{isSubmitting ? 'جاري الحفظ...' : 'متابعة'}</span> <FiArrowLeft />
        </Button>
      </div>
    </form>
  );
}