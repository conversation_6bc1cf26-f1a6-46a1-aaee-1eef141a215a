import React from 'react';
import { UseFormRegisterReturn } from 'react-hook-form';

interface Option {
  id: string;
  label: string;
}

interface RadioGroupProps {
  title: string;
  options: Option[];
  register: UseFormRegisterReturn;
  error?: string;
}

const RadioGroup: React.FC<RadioGroupProps> = ({ title, options, register, error }) => {
  return (
    <div className="p-6 bg-white rounded-lg border border-gray-200 shadow-sm">
      <h3 className="text-xl font-semibold mb-4">{title}</h3>
      <div className="space-y-3">
        {options.map((option) => (
          <label key={option.id} className="flex items-center p-4 border rounded-lg cursor-pointer hover:bg-gray-50">
            <input
              type="radio"
              value={option.id}
              {...register}
              className="form-radio h-5 w-5 text-[#AC8852] focus:ring-[#AC8852]"
            />
            <span className="mr-3 text-lg">{option.label}</span>
          </label>
        ))}
      </div>
      {error && <p className="mt-2 text-sm text-red-500">{error}</p>}
    </div>
  );
};

export default RadioGroup;