"use client";
import React, { useState } from 'react';
import { FiEye, FiEyeOff } from 'react-icons/fi';

interface PasswordInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label: string;
  id: string;
  icon: React.ReactNode;
  error?: string;
}

const PasswordInput = React.forwardRef<HTMLInputElement, PasswordInputProps>(
  ({ label, id, icon, error, ...props }, ref) => {
    const [showPassword, setShowPassword] = useState(false);

    return (
      <div className="w-full">
        <label htmlFor={id} className="flex items-center gap-2 mb-4 text-2xl font-medium text-black">
          {icon}
          <span>{label}</span>
          <span className="text-red-500">*</span>
        </label>
        <div className={`relative flex items-center w-full h-[95px] bg-white rounded-lg border border-solid transition ${error ? 'border-red-500' : 'border-[#cdcdcd]'} focus-within:ring-2 focus-within:ring-[#ac8852]`}>
          <input
            id={id}
            ref={ref}
            type={showPassword ? 'text' : 'password'}
            className="w-full h-full bg-transparent px-5 text-2xl text-center border-none outline-none"
            {...props}
          />
          <button
            type="button"
            onClick={() => setShowPassword(!showPassword)}
            className="absolute left-4 text-gray-500"
            aria-label={showPassword ? 'إخفاء كلمة المرور' : 'إظهار كلمة المرور'}
          >
            {showPassword ? <FiEyeOff size={24} /> : <FiEye size={24} />}
          </button>
        </div>
        {error && <p className="mt-2 text-sm text-red-500">{error}</p>}
      </div>
    );
  }
);
PasswordInput.displayName = 'PasswordInput';

export default PasswordInput;