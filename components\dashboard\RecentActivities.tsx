import { Activity } from '@/types';
import ActivityItem from './ActivityItem';
import Link from 'next/link';

const RecentActivities = ({ activities }: { activities: Activity[] }) => {
  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-bold text-gray-800">النشاطات الاخيرة</h3>
        <Link href="/dashboard/activities" className="text-sm font-medium text-[#AC8852] hover:underline">
            عرض الكل
        </Link>
      </div>
      <div className="space-y-2">
        {activities.map((activity) => (
          <ActivityItem key={activity.id} activity={activity} />
        ))}
      </div>
    </div>
  );
};

export default RecentActivities;