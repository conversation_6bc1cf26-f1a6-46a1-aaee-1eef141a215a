import Image from 'next/image';
import Link from 'next/link';

interface AccountTypeCardProps {
  iconSrc: string;
  title: string;
  description: string;
  href: string;
}

const AccountTypeCard: React.FC<AccountTypeCardProps> = ({ iconSrc, title, description, href }) => {
  return (
    <Link href={href} className="group">
      <article className="flex flex-col items-center text-center p-8 w-full h-[537px] bg-white rounded-[25px] border border-solid border-[#a5a4a4] shadow-lg hover:shadow-2xl transition-shadow duration-300 cursor-pointer">
        <div className="relative w-[140px] h-[140px] mt-8 mb-12">
          <Image
            src={iconSrc}
            alt={`${title} icon`}
            fill
            className="object-contain"
          />
        </div>
        <h2 className="font-medium text-black text-[40px] leading-tight mb-6">
          {title}
        </h2>
        <p className="font-medium text-[#807f7f] text-[35px] leading-[50px] max-w-[420px]">
          {description}
        </p>
      </article>
    </Link>
  );
};

export default AccountTypeCard;