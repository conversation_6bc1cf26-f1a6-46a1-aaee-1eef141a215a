import Image from 'next/image';

interface EntityTypeCardProps {
  title: string;
  imageSrc: string;
  isSelected: boolean;
  onSelect: () => void;
}

const EntityTypeCard: React.FC<EntityTypeCardProps> = ({ title, imageSrc, isSelected, onSelect }) => {
  const borderClasses = isSelected
    ? 'border-[#ac8852] shadow-[0px_0px_25px_rgba(172,136,82,0.25)]'
    : 'border-[#a5a4a4] shadow-lg';

  return (
    <button
      onClick={onSelect}
      className={`flex flex-col items-center justify-center text-center p-6 w-full h-[270px] bg-white rounded-[25px] border border-solid hover:border-[#ac8852] hover:shadow-[0px_0px_25px_rgba(172,136,82,0.25)] transition-all duration-300 ${borderClasses}`}
    >
      <div className="relative w-[85px] h-[85px] mb-6">
        <Image src={imageSrc} alt={title} fill className="object-contain" />
      </div>
      <h3
        className="font-medium text-black text-[30px] md:text-[40px] leading-tight"
        dangerouslySetInnerHTML={{ __html: title }} // To handle <br /> for "جهة حكومية"
      />
    </button>
  );
};

export default EntityTypeCard;