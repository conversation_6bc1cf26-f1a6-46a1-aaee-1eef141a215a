// ملف Button.tsx

import React from 'react';
import { cva, type VariantProps } from 'class-variance-authority';
import { twMerge } from 'tailwind-merge';

// تعريف أنماط الأزرار باستخدام cva
const buttonVariants = cva(
  // الأنماط المشتركة بين جميع الأزرار
  'flex items-center justify-center gap-3 rounded-[50px] border-[3px] font-medium text-[25px] transition-colors duration-300 disabled:opacity-50 disabled:pointer-events-none whitespace-nowrap',
  {
    variants: {
      // أنواع الزر (variant)
      variant: {
        outlineWhite: 'border-white text-white hover:bg-white hover:text-[#0d123d]',
        outlineGold: 'border-[#ac8852] text-[#ac8852] hover:bg-[#ac8852] hover:text-white',
        ghost: 'bg-violet-100 text-[#0c1338] border-transparent hover:bg-violet-200',
        primary: 'bg-[#ac8852] text-white border-[#ac8852] hover:bg-[#9a7749]', // نمط جديد أضيف
      },
      // أحجام الزر (size)
      size: {
        default: 'h-[95px] px-16 py-8',
        large: 'h-[100px] w-full max-w-[879px] px-8 py-9',
      },
    },
    // القيم الافتراضية
    defaultVariants: {
      variant: 'outlineGold', // تم تغيير النمط الافتراضي إلى primary
      size: 'default',
    },
  }
);

// تعريف خصائص الزر (بما في ذلك variant و size)
export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {}

// تعريف مكون الزر
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, ...props }, ref) => {
    return (
      <button
        className={twMerge(buttonVariants({ variant, size, className }))} // دمج الكلاسات
        ref={ref}
        {...props}
      />
      // يتم تمرير variant و size إلى buttonVariants لتحديد الأنماط المناسبة
    );
  }
);

Button.displayName = 'Button';

// تصدير المكون والأنماط
export { Button, buttonVariants };