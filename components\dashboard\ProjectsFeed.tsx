"use client";

import { useState } from 'react';
import { Project } from '@/types';
import ProjectCard from './ProjectCard'; // نستخدم المكون الذي قمنا بإنشائه في الرد السابق
import Link from 'next/link';

const tabs = ["جميع المشاريع", "شوهدت مؤخرا", "المشاريع المحفوظة"];

const ProjectsFeed = ({ projects }: { projects: Project[] }) => {
  const [activeTab, setActiveTab] = useState(tabs[0]);

  // في تطبيق حقيقي، ستقوم بفلترة المشاريع بناءً على التبويب النشط
  const filteredProjects = projects; 

  return (
    <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-xl font-bold text-gray-800">المشاريع الحديثة</h3>
        <Link href="/dashboard/projects" className="text-sm font-medium text-[#AC8852] hover:underline">
          عرض الكل
        </Link>
      </div>
      
      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="flex gap-6 -mb-px">
          {tabs.map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`py-3 px-1 text-lg font-medium transition-colors ${
                activeTab === tab
                  ? 'text-[#AC8852] border-b-2 border-[#AC8852]'
                  : 'text-gray-500 hover:text-gray-800'
              }`}
            >
              {tab}
            </button>
          ))}
        </nav>
      </div>
      
      {/* Projects Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-6">
        {filteredProjects.map((project) => (
          <ProjectCard key={project.id} project={project} />
        ))}
      </div>
    </div>
  );
};

export default ProjectsFeed;